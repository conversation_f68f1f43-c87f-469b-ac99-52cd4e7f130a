# 数据库字段映射说明

## 问题解决

### 🐛 问题描述
获取树洞详情接口返回500错误，错误信息显示：
```
Unknown column 'u.avatar' in 'field list'
```

### 🔍 问题原因
在MyBatis查询中引用了`u.avatar`字段，但实际的`users`表中没有`avatar`字段，只有`wechat_avatar_url`字段。

### ✅ 解决方案
修改MyBatis映射文件，将`u.avatar`替换为`u.wechat_avatar_url`。

## 📊 Users表字段映射

### 实际数据库字段 → API响应字段

| 数据库字段 | Java实体字段 | API响应字段 | 说明 |
|------------|--------------|-------------|------|
| `id` | `id` | `id` | 用户ID |
| `username` | `username` | `username` | 用户名 |
| `password` | `password` | - | 密码（不返回给前端） |
| `email` | `email` | - | 邮箱（隐私字段） |
| `phone` | `phone` | - | 手机号（隐私字段） |
| `wechat_openid` | `wechatOpenid` | - | 微信OpenID（隐私字段） |
| `wechat_unionid` | `wechatUnionid` | - | 微信UnionID（隐私字段） |
| `wechat_nickname` | `wechatNickname` | - | 微信昵称（隐私字段） |
| `wechat_avatar_url` | `wechatAvatarUrl` | `avatar` | **头像URL** |
| `daily_whisper_limit` | `dailyWhisperLimit` | - | 每日发布限制 |
| `last_login` | `lastLogin` | - | 最后登录时间 |
| `created_at` | `createdAt` | - | 创建时间 |
| `updated_at` | `updatedAt` | - | 更新时间 |

### 🔑 重要映射说明

**头像字段映射：**
- **数据库字段**：`wechat_avatar_url`
- **API返回字段**：`avatar`
- **映射逻辑**：
  ```sql
  CASE 
      WHEN w.is_anonymous = TRUE THEN NULL
      ELSE u.wechat_avatar_url
  END as 'userInfo.avatar'
  ```

## 📝 修改的文件

### 1. WhisperMapper.xml
修改了两处查询：
- `findRecentWhispers` - 获取树洞列表
- `findWhisperDetail` - 获取树洞详情

### 2. CommentMapper.xml
修改了评论查询：
- `findByWhisperId` - 获取评论列表

## 🎯 API响应示例

### 匿名用户信息
```json
{
  "userInfo": {
    "id": null,
    "username": "匿名用户",
    "avatar": null
  }
}
```

### 实名用户信息
```json
{
  "userInfo": {
    "id": 123,
    "username": "张三",
    "avatar": "https://wx.qlogo.cn/mmopen/..."
  }
}
```

## 🔒 隐私保护

### 匿名发布时
- `userInfo.id` → `null`
- `userInfo.username` → `"匿名用户"`
- `userInfo.avatar` → `null`

### 实名发布时
- `userInfo.id` → 真实用户ID
- `userInfo.username` → 真实用户名
- `userInfo.avatar` → 微信头像URL

## 🚀 扩展建议

如果将来需要支持用户自定义头像，可以考虑：

### 方案1：添加avatar字段
```sql
ALTER TABLE users ADD COLUMN avatar VARCHAR(500) COMMENT '用户头像URL';
```

### 方案2：头像优先级
```sql
CASE 
    WHEN w.is_anonymous = TRUE THEN NULL
    WHEN u.avatar IS NOT NULL THEN u.avatar
    ELSE u.wechat_avatar_url
END as 'userInfo.avatar'
```

这样可以支持：
1. 用户自定义头像优先
2. 没有自定义头像时使用微信头像
3. 匿名时不显示头像

## ✅ 验证方法

修改完成后，可以通过以下方式验证：

1. **获取树洞列表**：
   ```bash
   curl -X GET "http://localhost:8080/api/whispers"
   ```

2. **获取树洞详情**：
   ```bash
   curl -X GET "http://localhost:8080/api/whispers/1"
   ```

3. **获取评论列表**：
   ```bash
   curl -X GET "http://localhost:8080/api/whispers/1/comments"
   ```

现在这些接口应该都能正常返回数据，不再出现字段不存在的错误。
