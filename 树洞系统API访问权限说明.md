# 树洞系统API访问权限说明

## 🔓 无需登录的接口（匿名访问）

以下接口可以在不提供JWT token的情况下直接访问：

### 查询类接口

| 方法 | 路径 | 功能 | 说明 |
|------|------|------|------|
| GET | `/api/whispers` | 获取树洞列表 | 支持分页，匿名用户看不到互动状态 |
| GET | `/api/whispers/{id}` | 获取树洞详情 | 匿名用户的has_hugged和has_resonated为false |
| GET | `/api/whispers/{id}/comments` | 获取评论列表 | 支持分页 |

### 用户相关接口

| 方法 | 路径 | 功能 |
|------|------|------|
| POST | `/api/login` | 用户登录 |
| POST | `/api/register` | 用户注册 |
| POST | `/api/wechat-login` | 微信登录 |
| POST | `/api/send-reset-code` | 发送重置密码验证码 |
| POST | `/api/reset-password` | 重置密码 |
| POST | `/api/send-register-code` | 发送注册验证码 |

### 系统接口

| 路径 | 功能 |
|------|------|
| `/v3/api-docs/**` | API文档 |
| `/swagger-ui/**` | Swagger UI |
| `/swagger-ui.html` | Swagger UI首页 |

## 🔒 需要登录的接口（需要JWT token）

以下接口必须在请求头中提供有效的JWT token：

```
Authorization: Bearer <your_jwt_token>
```

### 树洞操作接口

| 方法 | 路径 | 功能 | 错误码 |
|------|------|------|--------|
| POST | `/api/whispers` | 发布树洞 | 401: 未授权 |
| POST | `/api/whispers/{id}/hug` | 拥抱树洞 | 401: 未授权 |
| POST | `/api/whispers/{id}/resonate` | 共鸣树洞 | 401: 未授权 |
| POST | `/api/whispers/{id}/comments` | 评论树洞 | 401: 未授权 |

## 📱 使用示例

### 匿名访问示例

```bash
# 获取树洞列表（无需token）
curl -X GET "http://localhost:8080/api/whispers?page=1&pageSize=5"

# 获取树洞详情（无需token）
curl -X GET "http://localhost:8080/api/whispers/123"

# 获取评论列表（无需token）
curl -X GET "http://localhost:8080/api/whispers/123/comments?page=1&pageSize=10"
```

### 登录用户访问示例

```bash
# 发布树洞（需要token）
curl -X POST "http://localhost:8080/api/whispers" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"content": "这是我的树洞内容", "is_anonymous": true}'

# 拥抱树洞（需要token）
curl -X POST "http://localhost:8080/api/whispers/123/hug" \
  -H "Authorization: Bearer your_jwt_token"

# 共鸣树洞（需要token）
curl -X POST "http://localhost:8080/api/whispers/123/resonate" \
  -H "Authorization: Bearer your_jwt_token"

# 评论树洞（需要token）
curl -X POST "http://localhost:8080/api/whispers/123/comments" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"content": "这是我的评论", "is_anonymous": false}'
```

## 🔍 匿名用户与登录用户的区别

### 查询树洞列表时

**匿名用户响应：**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 123,
        "content": "树洞内容...",
        "has_hugged": false,
        "has_resonated": false
      }
    ]
  }
}
```

**登录用户响应：**
```json
{
  "code": 200,
  "data": {
    "items": [
      {
        "id": 123,
        "content": "树洞内容...",
        "has_hugged": true,
        "has_resonated": false
      }
    ]
  }
}
```

### 查询树洞详情时

- **匿名用户**：`has_hugged`和`has_resonated`始终为`false`
- **登录用户**：显示真实的互动状态

## ⚠️ 注意事项

1. **Token格式**：必须以`Bearer `开头，后跟实际的JWT token
2. **Token有效性**：过期或无效的token会被忽略，按匿名用户处理
3. **错误处理**：需要登录的接口在未提供有效token时返回401错误
4. **匿名限制**：匿名用户无法进行任何写操作（发布、拥抱、共鸣、评论）

## 🎯 推荐使用方式

1. **浏览模式**：用户可以先匿名浏览树洞内容
2. **互动模式**：当用户想要互动时，引导其登录
3. **渐进式体验**：提供良好的匿名浏览体验，降低使用门槛

这样的设计既保护了需要认证的操作，又允许用户自由浏览内容，提供了良好的用户体验。
