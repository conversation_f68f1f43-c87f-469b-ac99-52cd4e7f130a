package com.xlxw.code.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ValidationUtil 测试类
 */
public class ValidationUtilTest {

    @Test
    public void testValidEmail() {
        // 测试有效邮箱
        assertTrue(ValidationUtil.isValidEmail("<EMAIL>"));
        assertTrue(ValidationUtil.isValidEmail("<EMAIL>"));
        assertTrue(ValidationUtil.isValidEmail("<EMAIL>"));
        
        // 测试无效邮箱
        assertFalse(ValidationUtil.isValidEmail("invalid-email"));
        assertFalse(ValidationUtil.isValidEmail("@domain.com"));
        assertFalse(ValidationUtil.isValidEmail("user@"));
        assertFalse(ValidationUtil.isValidEmail(""));
        assertFalse(ValidationUtil.isValidEmail(null));
    }

    @Test
    public void testValidPhone() {
        // 测试有效手机号
        assertTrue(ValidationUtil.isValidPhone("13812345678"));
        assertTrue(ValidationUtil.isValidPhone("15987654321"));
        assertTrue(ValidationUtil.isValidPhone("18612345678"));
        
        // 测试无效手机号
        assertFalse(ValidationUtil.isValidPhone("12812345678")); // 不是1开头的有效号段
        assertFalse(ValidationUtil.isValidPhone("1381234567"));  // 少一位
        assertFalse(ValidationUtil.isValidPhone("138123456789")); // 多一位
        assertFalse(ValidationUtil.isValidPhone(""));
        assertFalse(ValidationUtil.isValidPhone(null));
    }

    @Test
    public void testGetContactType() {
        // 测试邮箱类型
        assertEquals(ValidationUtil.ContactType.EMAIL, ValidationUtil.getContactType("<EMAIL>"));
        assertEquals(ValidationUtil.ContactType.EMAIL, ValidationUtil.getContactType("<EMAIL>"));
        
        // 测试手机号类型
        assertEquals(ValidationUtil.ContactType.PHONE, ValidationUtil.getContactType("13812345678"));
        assertEquals(ValidationUtil.ContactType.PHONE, ValidationUtil.getContactType("15987654321"));
        
        // 测试无效类型
        assertEquals(ValidationUtil.ContactType.INVALID, ValidationUtil.getContactType("invalid"));
        assertEquals(ValidationUtil.ContactType.INVALID, ValidationUtil.getContactType(""));
        assertEquals(ValidationUtil.ContactType.INVALID, ValidationUtil.getContactType(null));
    }
}
