spring:
  profiles:
    active: dev
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
  mail:
    host: smtp.qq.com
    port: 587
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          ssl:
            enable: false
          connectiontimeout: 5000
          timeout: 5000
          writetimeout: 5000

mybatis:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

jwt:
  secret: "cereshuzhitingnizhenbangcereshuzhitingnizhenbangsdawadasdwadwwwwwwwwwwwdadwdrwa"
  expiration: 86400000  # 24小时过期

server:
  port: 8080
  servlet:
    context-path: /api
