<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xlxw.code.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.xlxw.code.pojo.entity.User">
        <id column="id" property="id"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="email" property="email"/>
        <result column="phone" property="phone"/>
        <result column="wechat_openid" property="wechatOpenid"/>
        <result column="wechat_unionid" property="wechatUnionid"/>
        <result column="wechat_nickname" property="wechatNickname"/>
        <result column="wechat_avatar_url" property="wechatAvatarUrl"/>
        <result column="daily_whisper_limit" property="dailyWhisperLimit"/>
        <result column="last_login" property="lastLogin"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 根据ID查找用户 -->
    <select id="findById" parameterType="int" resultMap="BaseResultMap">
        SELECT id, username, password, email, phone, wechat_openid, wechat_unionid, wechat_nickname, wechat_avatar_url, daily_whisper_limit, last_login, created_at, updated_at
        FROM users
        WHERE id = #{id}
    </select>

    <sql id="Base_Column_List">
        id, username, password, email, phone, wechat_openid, wechat_unionid,
        wechat_nickname, wechat_avatar_url, daily_whisper_limit, last_login,
        created_at, updated_at
    </sql>

    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE username = #{username}
    </select>

    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE email = #{email}
    </select>

    <select id="findByWechatOpenId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE wechat_openid = #{openId}
    </select>

    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM users
        WHERE phone = #{phone}
    </select>

    <insert id="insert" parameterType="com.xlxw.code.pojo.entity.User" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO users (
            username, password, email, phone,
            wechat_openid, wechat_unionid, wechat_nickname, wechat_avatar_url,
            daily_whisper_limit, created_at, updated_at
        )
        VALUES (
            #{username}, #{password}, #{email}, #{phone},
            #{wechatOpenid}, #{wechatUnionid}, #{wechatNickname}, #{wechatAvatarUrl},
            #{dailyWhisperLimit}, NOW(), NOW()
        )
    </insert>

    <update id="updateById" parameterType="com.xlxw.code.pojo.entity.User">
        UPDATE users
        <set>
            <if test="email != null">email = #{email},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="wechatNickname != null">wechat_nickname = #{wechatNickname},</if>
            <if test="wechatAvatarUrl != null">wechat_avatar_url = #{wechatAvatarUrl},</if>
            <if test="lastLogin != null">last_login = #{lastLogin},</if>
            updated_at = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="updatePassword">
        UPDATE users
        SET password = #{newPassword},
            updated_at = NOW()
        WHERE id = #{id}
    </update>
</mapper>
