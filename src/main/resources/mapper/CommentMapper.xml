<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xlxw.code.mapper.CommentMapper">

    <!-- 插入评论 -->
    <insert id="insert" parameterType="com.xlxw.code.pojo.entity.Comment" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO comments (whisper_id, user_id, content, is_anonymous, created_at, updated_at)
        VALUES (#{whisperId}, #{userId}, #{content}, #{isAnonymous}, #{createdAt}, #{updatedAt})
    </insert>

    <!-- 统计树洞的评论数量 -->
    <select id="countByWhisperId" parameterType="int" resultType="int">
        SELECT COUNT(*)
        FROM comments
        WHERE whisper_id = #{whisperId}
    </select>

    <!-- 分页查询树洞评论 -->
    <select id="findByWhisperId" resultType="com.xlxw.code.pojo.vo.CommentVO">
        SELECT 
            c.id,
            c.content,
            c.is_anonymous,
            c.created_at,
            CASE 
                WHEN c.is_anonymous = TRUE THEN NULL
                ELSE u.id
            END as 'userInfo.id',
            CASE 
                WHEN c.is_anonymous = TRUE THEN '匿名用户'
                ELSE u.username
            END as 'userInfo.username',
            CASE 
                WHEN c.is_anonymous = TRUE THEN NULL
                ELSE u.avatar
            END as 'userInfo.avatar'
        FROM comments c
        LEFT JOIN users u ON c.user_id = u.id
        WHERE c.whisper_id = #{whisperId}
        ORDER BY c.created_at ASC
        LIMIT #{offset}, #{pageSize}
    </select>

</mapper>
