<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xlxw.code.mapper.WhisperMapper">

    <!-- 插入树洞 -->
    <insert id="insert" parameterType="com.xlxw.code.pojo.entity.Whisper" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO whispers (user_id, content, is_anonymous, hug_count, resonance_count, comment_count, created_at, updated_at)
        VALUES (#{userId}, #{content}, #{isAnonymous}, #{hugCount}, #{resonanceCount}, #{commentCount}, #{createdAt}, #{updatedAt})
    </insert>

    <!-- 根据ID查找树洞 -->
    <select id="findById" parameterType="int" resultType="com.xlxw.code.pojo.entity.Whisper">
        SELECT id, user_id, content, is_anonymous, hug_count, resonance_count, comment_count, created_at, updated_at
        FROM whispers
        WHERE id = #{id}
    </select>

    <!-- 更新拥抱数量 -->
    <update id="updateHugCount">
        UPDATE whispers SET hug_count = #{hugCount}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新共鸣数量 -->
    <update id="updateResonanceCount">
        UPDATE whispers SET resonance_count = #{resonanceCount}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 更新评论数量 -->
    <update id="updateCommentCount">
        UPDATE whispers SET comment_count = #{commentCount}, updated_at = NOW()
        WHERE id = #{id}
    </update>

    <!-- 分页查询树洞列表（两周内） -->
    <select id="findRecentWhispers" resultType="com.xlxw.code.pojo.vo.WhisperVO">
        SELECT 
            w.id,
            w.content,
            w.is_anonymous,
            w.hug_count,
            w.resonance_count,
            w.comment_count,
            w.created_at,
            CASE 
                WHEN w.is_anonymous = TRUE THEN NULL
                ELSE u.id
            END as 'userInfo.id',
            CASE 
                WHEN w.is_anonymous = TRUE THEN '匿名用户'
                ELSE u.username
            END as 'userInfo.username',
            CASE
                WHEN w.is_anonymous = TRUE THEN NULL
                ELSE u.wechat_avatar_url
            END as 'userInfo.avatar',
            <if test="currentUserId != null">
                CASE 
                    WHEN h.id IS NOT NULL THEN TRUE
                    ELSE FALSE
                END as has_hugged,
                CASE 
                    WHEN r.id IS NOT NULL THEN TRUE
                    ELSE FALSE
                END as has_resonated
            </if>
            <if test="currentUserId == null">
                FALSE as has_hugged,
                FALSE as has_resonated
            </if>
        FROM whispers w
        LEFT JOIN users u ON w.user_id = u.id
        <if test="currentUserId != null">
            LEFT JOIN hugs h ON w.id = h.whisper_id AND h.user_id = #{currentUserId}
            LEFT JOIN resonances r ON w.id = r.whisper_id AND r.user_id = #{currentUserId}
        </if>
        WHERE w.created_at >= DATE_SUB(NOW(), INTERVAL 14 DAY)
        ORDER BY w.created_at DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计两周内树洞总数 -->
    <select id="countRecentWhispers" resultType="long">
        SELECT COUNT(*)
        FROM whispers
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 14 DAY)
    </select>

    <!-- 根据ID查询树洞详情 -->
    <select id="findWhisperDetail" resultType="com.xlxw.code.pojo.vo.WhisperVO">
        SELECT 
            w.id,
            w.content,
            w.is_anonymous,
            w.hug_count,
            w.resonance_count,
            w.comment_count,
            w.created_at,
            CASE 
                WHEN w.is_anonymous = TRUE THEN NULL
                ELSE u.id
            END as 'userInfo.id',
            CASE 
                WHEN w.is_anonymous = TRUE THEN '匿名用户'
                ELSE u.username
            END as 'userInfo.username',
            CASE
                WHEN w.is_anonymous = TRUE THEN NULL
                ELSE u.wechat_avatar_url
            END as 'userInfo.avatar',
            <if test="currentUserId != null">
                CASE 
                    WHEN h.id IS NOT NULL THEN TRUE
                    ELSE FALSE
                END as has_hugged,
                CASE 
                    WHEN r.id IS NOT NULL THEN TRUE
                    ELSE FALSE
                END as has_resonated
            </if>
            <if test="currentUserId == null">
                FALSE as has_hugged,
                FALSE as has_resonated
            </if>
        FROM whispers w
        LEFT JOIN users u ON w.user_id = u.id
        <if test="currentUserId != null">
            LEFT JOIN hugs h ON w.id = h.whisper_id AND h.user_id = #{currentUserId}
            LEFT JOIN resonances r ON w.id = r.whisper_id AND r.user_id = #{currentUserId}
        </if>
        WHERE w.id = #{id}
    </select>

    <!-- 删除超过14天的树洞 -->
    <delete id="deleteOldWhispers">
        DELETE FROM whispers 
        WHERE created_at &lt; DATE_SUB(NOW(), INTERVAL 14 DAY)
    </delete>

</mapper>
