<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xlxw.code.mapper.HugMapper">

    <!-- 插入拥抱记录 -->
    <insert id="insert" parameterType="com.xlxw.code.pojo.entity.Hug" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO hugs (whisper_id, user_id, created_at)
        VALUES (#{whisperId}, #{userId}, #{createdAt})
    </insert>

    <!-- 检查用户是否已拥抱过该树洞 -->
    <select id="existsByWhisperIdAndUserId" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM hugs
        WHERE whisper_id = #{whisperId} AND user_id = #{userId}
    </select>

    <!-- 统计树洞的拥抱数量 -->
    <select id="countByWhisperId" parameterType="int" resultType="int">
        SELECT COUNT(*)
        FROM hugs
        WHERE whisper_id = #{whisperId}
    </select>

</mapper>
