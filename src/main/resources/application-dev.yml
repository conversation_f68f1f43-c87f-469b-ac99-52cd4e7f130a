spring:
  datasource:
    url: *****************************************************************************************************
    username: root
    password: qianshi.
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>  # 替换为您的QQ邮箱
    password: igpdcmcvodvdfcai     # 替换为您的QQ邮箱授权码
    properties:
      mail:
        smtp:
          auth: true
          ssl:
            enable: true
            required: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
    default-encoding: UTF-8

logging:
  level:
    com.xlxw.code: debug
    org.springframework: info

# 开发环境关闭swagger权限验证
springdoc:
  swagger-ui:
    enabled: true
  api-docs:
    enabled: true
