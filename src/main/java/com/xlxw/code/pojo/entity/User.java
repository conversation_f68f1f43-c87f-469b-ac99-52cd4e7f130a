package com.xlxw.code.pojo.entity;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class User {
    private Integer id;
    private String username;
    private String password;
    private String email;
    private String phone;
    private String wechatOpenid;
    private String wechatUnionid;
    private String wechatNickname;
    private String wechatAvatarUrl;
    private Integer dailyWhisperLimit;
    private LocalDateTime lastLogin;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
