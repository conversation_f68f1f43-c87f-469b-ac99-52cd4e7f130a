package com.xlxw.code.pojo.vo;

import lombok.Data;
import java.util.List;

/**
 * 分页视图对象
 */
@Data
public class PageVO<T> {
    private List<T> items;
    private PaginationVO pagination;
    
    @Data
    public static class PaginationVO {
        private Integer currentPage;
        private Integer pageSize;
        private Long totalItems;
        private Integer totalPages;
        
        public PaginationVO(Integer currentPage, Integer pageSize, Long totalItems) {
            this.currentPage = currentPage;
            this.pageSize = pageSize;
            this.totalItems = totalItems;
            this.totalPages = (int) Math.ceil((double) totalItems / pageSize);
        }
    }
    
    public PageVO(List<T> items, Integer currentPage, Integer pageSize, Long totalItems) {
        this.items = items;
        this.pagination = new PaginationVO(currentPage, pageSize, totalItems);
    }
}
