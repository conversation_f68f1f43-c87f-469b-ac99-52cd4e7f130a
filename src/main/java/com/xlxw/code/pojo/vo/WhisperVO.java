package com.xlxw.code.pojo.vo;

import lombok.Data;
import java.time.LocalDateTime;

/**
 * 树洞视图对象
 */
@Data
public class WhisperVO {
    private Integer id;
    private String content;
    private Boolean isAnonymous;
    private Integer hugCount;
    private Integer resonanceCount;
    private Integer commentCount;
    private LocalDateTime createdAt;
    private UserInfoVO userInfo;
    private Boolean hasHugged;
    private Boolean hasResonated;
    
    @Data
    public static class UserInfoVO {
        private Integer id;
        private String username;
        private String avatar;
    }
}
