package com.xlxw.code.task;

import com.xlxw.code.service.WhisperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 树洞清理定时任务
 */
@Slf4j
@Component
public class WhisperCleanupTask {

    @Autowired
    private WhisperService whisperService;

    /**
     * 每天凌晨2点执行清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldWhispers() {
        log.info("开始执行树洞清理任务...");
        try {
            whisperService.cleanupOldWhispers();
            log.info("树洞清理任务执行完成");
        } catch (Exception e) {
            log.error("树洞清理任务执行失败", e);
        }
    }
}
