package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.Comment;
import com.xlxw.code.pojo.vo.CommentVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CommentMapper {
    
    /**
     * 插入评论
     */
    int insert(Comment comment);
    
    /**
     * 统计树洞的评论数量
     */
    Integer countByWhisperId(Integer whisperId);
    
    /**
     * 分页查询树洞评论
     */
    List<CommentVO> findByWhisperId(@Param("whisperId") Integer whisperId,
                                    @Param("offset") Integer offset,
                                    @Param("pageSize") Integer pageSize);
}
