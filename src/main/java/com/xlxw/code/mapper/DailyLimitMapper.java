package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.DailyLimit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

@Mapper
public interface DailyLimitMapper {
    
    /**
     * 插入每日限制记录
     */
    int insert(DailyLimit dailyLimit);
    
    /**
     * 根据用户ID和日期查找记录
     */
    DailyLimit findByUserIdAndDate(@Param("userId") Integer userId, 
                                   @Param("date") LocalDate date);
    
    /**
     * 更新树洞发布次数
     */
    int updateWhisperCount(@Param("userId") Integer userId, 
                          @Param("date") LocalDate date, 
                          @Param("whisperCount") Integer whisperCount);
}
