package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.Hug;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface HugMapper {
    
    /**
     * 插入拥抱记录
     */
    int insert(Hug hug);
    
    /**
     * 检查用户是否已拥抱过该树洞
     */
    boolean existsByWhisperIdAndUserId(@Param("whisperId") Integer whisperId, 
                                       @Param("userId") Integer userId);
    
    /**
     * 统计树洞的拥抱数量
     */
    Integer countByWhisperId(Integer whisperId);
}
