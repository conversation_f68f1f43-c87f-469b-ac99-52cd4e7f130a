package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.Whisper;
import com.xlxw.code.pojo.vo.WhisperVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WhisperMapper {
    
    /**
     * 插入树洞
     */
    int insert(Whisper whisper);
    
    /**
     * 根据ID查找树洞
     */
    Whisper findById(Integer id);
    
    /**
     * 更新拥抱数量
     */
    int updateHugCount(@Param("id") Integer id, @Param("hugCount") Integer hugCount);
    
    /**
     * 更新共鸣数量
     */
    int updateResonanceCount(@Param("id") Integer id, @Param("resonanceCount") Integer resonanceCount);
    
    /**
     * 更新评论数量
     */
    int updateCommentCount(@Param("id") Integer id, @Param("commentCount") Integer commentCount);
    
    /**
     * 分页查询树洞列表（两周内）
     */
    List<WhisperVO> findRecentWhispers(@Param("offset") Integer offset, 
                                       @Param("pageSize") Integer pageSize,
                                       @Param("currentUserId") Integer currentUserId);
    
    /**
     * 统计两周内树洞总数
     */
    Long countRecentWhispers();
    
    /**
     * 根据ID查询树洞详情
     */
    WhisperVO findWhisperDetail(@Param("id") Integer id, @Param("currentUserId") Integer currentUserId);
    
    /**
     * 删除超过14天的树洞
     */
    int deleteOldWhispers();
}
