package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.Resonance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ResonanceMapper {
    
    /**
     * 插入共鸣记录
     */
    int insert(Resonance resonance);
    
    /**
     * 检查用户是否已共鸣过该树洞
     */
    boolean existsByWhisperIdAndUserId(@Param("whisperId") Integer whisperId, 
                                       @Param("userId") Integer userId);
    
    /**
     * 统计树洞的共鸣数量
     */
    Integer countByWhisperId(Integer whisperId);
}
