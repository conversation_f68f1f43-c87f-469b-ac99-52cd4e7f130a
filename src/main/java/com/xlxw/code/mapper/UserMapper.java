package com.xlxw.code.mapper;

import com.xlxw.code.pojo.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface UserMapper {
    User findById(Integer id);

    User findByUsername(String username);

    User findByEmail(String email);

    User findByWechatOpenId(String openId);

    User findByPhone(String phone);

    int insert(User user);

    int updateById(User user);

    int updatePassword(@Param("id") Integer id, @Param("newPassword") String newPassword);
}
