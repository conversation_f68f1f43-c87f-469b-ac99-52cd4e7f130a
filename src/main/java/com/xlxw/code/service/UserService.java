package com.xlxw.code.service;

import com.xlxw.code.pojo.dto.LoginDTO;
import com.xlxw.code.pojo.dto.RegisterDTO;
import com.xlxw.code.pojo.entity.User;

public interface UserService {
    /**
     * 用户登录
     */
    User login(LoginDTO loginDTO);

    /**
     * 用户注册
     */
    User register(RegisterDTO registerDTO);

    /**
     * 微信登录
     */
    User wechatLogin(String code);

    /**
     * 发送重置密码验证码
     * @param contact 联系方式（邮箱或手机号）
     */
    void sendResetCode(String contact);

    /**
     * 重置密码
     * @param contact 联系方式（邮箱或手机号）
     * @param verificationCode 验证码
     * @param newPassword 新密码
     */
    void resetPassword(String contact, String verificationCode, String newPassword);

    /**
     * 获取用户信息
     */
    User getUserInfo(Integer userId);

    /**
     * 更新用户信息
     */
    void updateUserInfo(Integer userId, User user);

    /**
     * 发送注册验证码
     * @param email 邮箱
     */
    void sendRegisterCode(String email);
}
