package com.xlxw.code.service;

import com.xlxw.code.pojo.dto.CommentCreateDTO;
import com.xlxw.code.pojo.dto.WhisperCreateDTO;
import com.xlxw.code.pojo.vo.CommentVO;
import com.xlxw.code.pojo.vo.PageVO;
import com.xlxw.code.pojo.vo.WhisperVO;

import java.util.Map;

/**
 * 树洞服务接口
 */
public interface WhisperService {
    
    /**
     * 发布树洞
     */
    Map<String, Object> createWhisper(Integer userId, WhisperCreateDTO dto);
    
    /**
     * 拥抱树洞
     */
    Map<String, Object> hugWhisper(Integer userId, Integer whisperId);
    
    /**
     * 共鸣树洞
     */
    Map<String, Object> resonateWhisper(Integer userId, Integer whisperId);
    
    /**
     * 评论树洞
     */
    Map<String, Object> commentWhisper(Integer userId, Integer whisperId, CommentCreateDTO dto);
    
    /**
     * 获取树洞列表（分页）
     */
    PageVO<WhisperVO> getWhisperList(Integer page, Integer pageSize, Integer currentUserId);
    
    /**
     * 获取树洞详情
     */
    WhisperVO getWhisperDetail(Integer whisperId, Integer currentUserId);
    
    /**
     * 获取树洞评论列表（分页）
     */
    PageVO<CommentVO> getWhisperComments(Integer whisperId, Integer page, Integer pageSize);
    
    /**
     * 清理过期树洞
     */
    void cleanupOldWhispers();
}
