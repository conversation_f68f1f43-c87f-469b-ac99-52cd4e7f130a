package com.xlxw.code.service.impl;

import com.xlxw.code.common.exception.BusinessException;
import com.xlxw.code.mapper.*;
import com.xlxw.code.pojo.dto.CommentCreateDTO;
import com.xlxw.code.pojo.dto.WhisperCreateDTO;
import com.xlxw.code.pojo.entity.*;
import com.xlxw.code.pojo.vo.CommentVO;
import com.xlxw.code.pojo.vo.PageVO;
import com.xlxw.code.pojo.vo.WhisperVO;
import com.xlxw.code.service.WhisperService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WhisperServiceImpl implements WhisperService {

    @Autowired
    private WhisperMapper whisperMapper;

    @Autowired
    private HugMapper hugMapper;

    @Autowired
    private ResonanceMapper resonanceMapper;

    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private DailyLimitMapper dailyLimitMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional
    public Map<String, Object> createWhisper(Integer userId, WhisperCreateDTO dto) {
        // 检查用户今日剩余发布次数
        LocalDate today = LocalDate.now();
        DailyLimit dailyLimit = dailyLimitMapper.findByUserIdAndDate(userId, today);

        User user = userMapper.findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        int dailyLimitCount = user.getDailyWhisperLimit();
        int currentCount = 0;

        if (dailyLimit != null) {
            currentCount = dailyLimit.getWhisperCount();
            if (currentCount >= dailyLimitCount) {
                throw new BusinessException("今日发布次数已达上限");
            }
        }

        // 创建树洞
        Whisper whisper = new Whisper();
        whisper.setUserId(userId);
        whisper.setContent(dto.getContent());
        whisper.setIsAnonymous(dto.getIsAnonymous());
        whisper.setHugCount(0);
        whisper.setResonanceCount(0);
        whisper.setCommentCount(0);
        whisper.setCreatedAt(LocalDateTime.now());
        whisper.setUpdatedAt(LocalDateTime.now());

        whisperMapper.insert(whisper);

        // 更新每日限制
        if (dailyLimit == null) {
            dailyLimit = new DailyLimit();
            dailyLimit.setUserId(userId);
            dailyLimit.setDate(today);
            dailyLimit.setWhisperCount(1);
            dailyLimit.setCreatedAt(LocalDateTime.now());
            dailyLimit.setUpdatedAt(LocalDateTime.now());
            dailyLimitMapper.insert(dailyLimit);
        } else {
            dailyLimitMapper.updateWhisperCount(userId, today, currentCount + 1);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("whisper_id", whisper.getId());
        result.put("remaining_daily_whispers", dailyLimitCount - currentCount - 1);
        result.put("daily_limit", dailyLimitCount);

        return result;
    }
    @Override
    @Transactional
    public Map<String, Object> hugWhisper(Integer userId, Integer whisperId) {
        // 检查树洞是否存在
        Whisper whisper = whisperMapper.findById(whisperId);
        if (whisper == null) {
            throw new BusinessException("树洞不存在");
        }

        // 检查是否已拥抱过
        if (hugMapper.existsByWhisperIdAndUserId(whisperId, userId)) {
            throw new BusinessException("已拥抱过该树洞");
        }

        // 创建拥抱记录
        Hug hug = new Hug();
        hug.setWhisperId(whisperId);
        hug.setUserId(userId);
        hug.setCreatedAt(LocalDateTime.now());
        hugMapper.insert(hug);

        // 更新树洞拥抱数量
        Integer hugCount = hugMapper.countByWhisperId(whisperId);
        whisperMapper.updateHugCount(whisperId, hugCount);

        Map<String, Object> result = new HashMap<>();
        result.put("hug_count", hugCount);
        return result;
    }

    @Override
    @Transactional
    public Map<String, Object> resonateWhisper(Integer userId, Integer whisperId) {
        // 检查树洞是否存在
        Whisper whisper = whisperMapper.findById(whisperId);
        if (whisper == null) {
            throw new BusinessException("树洞不存在");
        }

        // 检查是否已共鸣过
        if (resonanceMapper.existsByWhisperIdAndUserId(whisperId, userId)) {
            throw new BusinessException("已共鸣过该树洞");
        }

        // 创建共鸣记录
        Resonance resonance = new Resonance();
        resonance.setWhisperId(whisperId);
        resonance.setUserId(userId);
        resonance.setCreatedAt(LocalDateTime.now());
        resonanceMapper.insert(resonance);

        // 更新树洞共鸣数量
        Integer resonanceCount = resonanceMapper.countByWhisperId(whisperId);
        whisperMapper.updateResonanceCount(whisperId, resonanceCount);

        Map<String, Object> result = new HashMap<>();
        result.put("resonance_count", resonanceCount);
        return result;
    }
    @Override
    @Transactional
    public Map<String, Object> commentWhisper(Integer userId, Integer whisperId, CommentCreateDTO dto) {
        // 检查树洞是否存在
        Whisper whisper = whisperMapper.findById(whisperId);
        if (whisper == null) {
            throw new BusinessException("树洞不存在");
        }

        // 创建评论
        Comment comment = new Comment();
        comment.setWhisperId(whisperId);
        comment.setUserId(userId);
        comment.setContent(dto.getContent());
        comment.setIsAnonymous(dto.getIsAnonymous());
        comment.setCreatedAt(LocalDateTime.now());
        comment.setUpdatedAt(LocalDateTime.now());
        commentMapper.insert(comment);

        // 更新树洞评论数量
        Integer commentCount = commentMapper.countByWhisperId(whisperId);
        whisperMapper.updateCommentCount(whisperId, commentCount);

        Map<String, Object> result = new HashMap<>();
        result.put("comment_id", comment.getId());
        result.put("comment_count", commentCount);
        return result;
    }

    @Override
    public PageVO<WhisperVO> getWhisperList(Integer page, Integer pageSize, Integer currentUserId) {
        // 参数校验
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1 || pageSize > 50) pageSize = 5;

        Integer offset = (page - 1) * pageSize;

        // 查询数据
        List<WhisperVO> whispers = whisperMapper.findRecentWhispers(offset, pageSize, currentUserId);
        Long totalItems = whisperMapper.countRecentWhispers();

        return new PageVO<>(whispers, page, pageSize, totalItems);
    }

    @Override
    public WhisperVO getWhisperDetail(Integer whisperId, Integer currentUserId) {
        WhisperVO whisper = whisperMapper.findWhisperDetail(whisperId, currentUserId);
        if (whisper == null) {
            throw new BusinessException("树洞不存在");
        }
        return whisper;
    }

    @Override
    public PageVO<CommentVO> getWhisperComments(Integer whisperId, Integer page, Integer pageSize) {
        // 检查树洞是否存在
        Whisper whisper = whisperMapper.findById(whisperId);
        if (whisper == null) {
            throw new BusinessException("树洞不存在");
        }

        // 参数校验
        if (page == null || page < 1) page = 1;
        if (pageSize == null || pageSize < 1 || pageSize > 50) pageSize = 10;

        Integer offset = (page - 1) * pageSize;

        // 查询数据
        List<CommentVO> comments = commentMapper.findByWhisperId(whisperId, offset, pageSize);
        Long totalItems = commentMapper.countByWhisperId(whisperId).longValue();

        return new PageVO<>(comments, page, pageSize, totalItems);
    }

    @Override
    @Transactional
    public void cleanupOldWhispers() {
        int deletedCount = whisperMapper.deleteOldWhispers();
        log.info("清理过期树洞完成，删除了 {} 条记录", deletedCount);
    }
}