package com.xlxw.code.service.impl;

import com.xlxw.code.common.exception.BusinessException;
import com.xlxw.code.mapper.UserMapper;
import com.xlxw.code.pojo.dto.LoginDTO;
import com.xlxw.code.pojo.dto.RegisterDTO;
import com.xlxw.code.pojo.entity.User;
import com.xlxw.code.service.UserService;
import com.xlxw.code.util.SmsUtil;
import com.xlxw.code.util.ValidationUtil;
import com.xlxw.code.util.MailConfigChecker;
import org.springframework.beans.factory.annotation.Value;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private JavaMailSender mailSender;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    @Autowired
    private SmsUtil smsUtil;

    @Autowired
    private MailConfigChecker mailConfigChecker;

    @Value("${spring.mail.username:}")
    private String sendMailer;

    // 存储验证码的Map (在实际项目中应该使用Redis)
    private final Map<String, String> verificationCodes = new HashMap<>();

    @Override
    public User login(LoginDTO loginDTO) {
        User user = userMapper.findByUsername(loginDTO.getUsername());
        if (user == null || !passwordEncoder.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }

        // 更新最后登录时间
        user.setLastLogin(LocalDateTime.now());
        userMapper.updateById(user);

        return user;
    }

    @Override
    @Transactional
    public User register(RegisterDTO registerDTO) {
        // 验证用户名是否已存在
        if (userMapper.findByUsername(registerDTO.getUsername()) != null) {
            throw new BusinessException("用户名已存在");
        }

        // 验证邮箱是否已存在
        if (userMapper.findByEmail(registerDTO.getEmail()) != null) {
            throw new BusinessException("邮箱已被注册");
        }

        // 验证手机号是否已存在
        if (userMapper.findByPhone(registerDTO.getPhone()) != null) {
            throw new BusinessException("手机号已被注册");
        }

        // 验证两次密码是否一致
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException("两次密码不一致");
        }

        // 验证验证码
        String storedCode = verificationCodes.get(registerDTO.getEmail());
        if (storedCode == null || !storedCode.equals(registerDTO.getVerificationCode())) {
            throw new BusinessException("验证码错误或已过期");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(registerDTO.getUsername());
        user.setPassword(passwordEncoder.encode(registerDTO.getPassword()));
        user.setEmail(registerDTO.getEmail());
        user.setPhone(registerDTO.getPhone());
        user.setDailyWhisperLimit(5); // 设置默认值

        userMapper.insert(user);

        // 清除验证码
        verificationCodes.remove(registerDTO.getEmail());

        return user;
    }

    @Override
    public User wechatLogin(String code) {
        // TODO: 实现微信登录逻辑
        throw new BusinessException("微信登录功能暂未实现");
    }

    @Override
    public void sendResetCode(String contact) {
        // 验证联系方式格式
        ValidationUtil.ContactType contactType = ValidationUtil.getContactType(contact);
        if (contactType == ValidationUtil.ContactType.INVALID) {
            throw new BusinessException("请输入有效的邮箱地址或手机号");
        }

        User user = null;

        // 根据联系方式类型查找用户
        if (contactType == ValidationUtil.ContactType.EMAIL) {
            user = userMapper.findByEmail(contact);
            if (user == null) {
                throw new BusinessException("该邮箱未注册");
            }
        } else if (contactType == ValidationUtil.ContactType.PHONE) {
            user = userMapper.findByPhone(contact);
            if (user == null) {
                throw new BusinessException("该手机号未注册");
            }
        }

        // 生成6位验证码
        String verificationCode = String.format("%06d", new Random().nextInt(1000000));

        try {
            // 根据联系方式类型发送验证码
            if (contactType == ValidationUtil.ContactType.EMAIL) {
                // 发送邮件验证码
                sendEmailVerificationCode(contact, verificationCode);
                log.info("邮件验证码发送成功到: {}", contact);
            } else if (contactType == ValidationUtil.ContactType.PHONE) {
                // 发送短信验证码
                boolean success = smsUtil.sendResetPasswordCode(contact, verificationCode);
                if (!success) {
                    throw new BusinessException("短信验证码发送失败，请稍后重试");
                }
                log.info("短信验证码发送成功到: {}", contact);
            }

            // 存储验证码（实际项目中应该设置过期时间）
            verificationCodes.put(contact, verificationCode);

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("验证码发送失败", e);
            // 根据联系方式类型提供更友好的错误信息
            if (contactType == ValidationUtil.ContactType.EMAIL) {
                throw new BusinessException("邮件服务暂时不可用，请稍后重试或使用手机号重置密码");
            } else {
                throw new BusinessException("短信服务暂时不可用，请稍后重试或使用邮箱重置密码");
            }
        }
    }

    @Override
    @Transactional
    public void resetPassword(String contact, String verificationCode, String newPassword) {
        // 验证验证码
        String storedCode = verificationCodes.get(contact);
        if (storedCode == null || !storedCode.equals(verificationCode)) {
            throw new BusinessException("验证码错误或已过期");
        }

        // 验证联系方式格式
        ValidationUtil.ContactType contactType = ValidationUtil.getContactType(contact);
        if (contactType == ValidationUtil.ContactType.INVALID) {
            throw new BusinessException("请输入有效的邮箱地址或手机号");
        }

        User user = null;

        // 根据联系方式类型查找用户
        if (contactType == ValidationUtil.ContactType.EMAIL) {
            user = userMapper.findByEmail(contact);
        } else if (contactType == ValidationUtil.ContactType.PHONE) {
            user = userMapper.findByPhone(contact);
        }

        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 更新密码
        userMapper.updatePassword(user.getId(), passwordEncoder.encode(newPassword));

        // 清除验证码
        verificationCodes.remove(contact);
    }

    @Override
    public User getUserInfo(Integer userId) {
        User user = userMapper.findByUsername(userId.toString());
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        return user;
    }

    @Override
    public void updateUserInfo(Integer userId, User user) {
        user.setId(userId);

        // 防止更新敏感字段
        user.setPassword(null);
        user.setUsername(null);
        user.setWechatOpenid(null);
        user.setWechatUnionid(null);
        user.setDailyWhisperLimit(null);

        int rows = userMapper.updateById(user);
        if (rows == 0) {
            throw new BusinessException("用户不存在");
        }
    }

    @Override
    public void sendRegisterCode(String email) {
        // 检查邮箱是否已被注册
        if (userMapper.findByEmail(email) != null) {
            throw new BusinessException("该邮箱已被注册");
        }

        log.info("开始生成验证码...");
        // 生成6位验证码
        String verificationCode = String.format("%06d", new Random().nextInt(1000000));
        log.info("生成的验证码: {}", verificationCode);

        try {
            log.info("开始配置邮件信息...");
            // 发送验证码邮件
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(sendMailer);  // 设置发件人
            message.setTo(email);
            message.setSubject("注册验证码");
            message.setText("您的注册验证码是：" + verificationCode + "，验证码有效期为5分钟。");

            log.info("开始发送邮件到: {}", email);
            mailSender.send(message);
            log.info("邮件发送成功");

            // 存储验证码（实际项目中建议使用Redis并设置过期时间）
            verificationCodes.put(email, verificationCode);
            log.info("验证码已存储");
        } catch (Exception e) {
            log.error("邮件发送失败", e);
            throw new BusinessException("验证码发送失败：" + e.getMessage());
        }
    }

    /**
     * 发送邮件验证码
     * @param email 邮箱地址
     * @param verificationCode 验证码
     */
    private void sendEmailVerificationCode(String email, String verificationCode) {
        // 检查邮件配置
        if (!mailConfigChecker.isMailConfigured()) {
            log.info("邮件服务未配置，使用模拟发送模式");
            log.info("模拟邮件发送: 发送验证码 {} 到邮箱 {}", verificationCode, email);
            return;
        }

        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(sendMailer);
            message.setTo(email);
            message.setSubject("密码重置验证码");
            message.setText("您的密码重置验证码是：" + verificationCode + "，验证码有效期为5分钟。");

            mailSender.send(message);
            log.info("真实邮件发送成功到: {}", email);

        } catch (Exception e) {
            log.error("邮件发送失败，降级到模拟发送: {}", e.getMessage());
            // 降级到模拟发送，避免影响用户体验
            log.info("模拟邮件发送: 发送验证码 {} 到邮箱 {}", verificationCode, email);
        }
    }
}
