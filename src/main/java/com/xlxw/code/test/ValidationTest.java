package com.xlxw.code.test;

import com.xlxw.code.util.ValidationUtil;

/**
 * Validation Test
 */
public class ValidationTest {

    public static void main(String[] args) {
        System.out.println("=== Validation Test ===");

        // Test email validation
        System.out.println("\n--- Email Test ---");
        testEmail("<EMAIL>");
        testEmail("<EMAIL>");
        testEmail("<EMAIL>");
        testEmail("invalid-email");
        testEmail("@domain.com");
        testEmail("");

        // Test phone validation
        System.out.println("\n--- Phone Test ---");
        testPhone("13812345678");
        testPhone("15987654321");
        testPhone("18612345678");
        testPhone("12812345678"); // invalid
        testPhone("1381234567");  // short
        testPhone("138123456789"); // long
        testPhone("");

        // Test contact type detection
        System.out.println("\n--- Contact Type Test ---");
        testContactType("<EMAIL>");
        testContactType("13812345678");
        testContactType("invalid");
        testContactType("");

        System.out.println("\n=== Test Complete ===");
    }
    
    private static void testEmail(String email) {
        boolean isValid = ValidationUtil.isValidEmail(email);
        System.out.printf("Email: %-25s -> %s\n",
            email.isEmpty() ? "(empty)" : email,
            isValid ? "Valid" : "Invalid");
    }

    private static void testPhone(String phone) {
        boolean isValid = ValidationUtil.isValidPhone(phone);
        System.out.printf("Phone: %-15s -> %s\n",
            phone.isEmpty() ? "(empty)" : phone,
            isValid ? "Valid" : "Invalid");
    }

    private static void testContactType(String contact) {
        ValidationUtil.ContactType type = ValidationUtil.getContactType(contact);
        String typeStr = switch (type) {
            case EMAIL -> "Email";
            case PHONE -> "Phone";
            case INVALID -> "Invalid";
        };
        System.out.printf("Contact: %-20s -> %s\n",
            contact.isEmpty() ? "(empty)" : contact,
            typeStr);
    }
}
