package com.xlxw.code.common.exception;

import com.xlxw.code.common.response.R;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public R handleBusinessException(BusinessException e) {
        return R.error(e.getCode(), e.getMessage());
    }

    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R handleValidationException(Exception e) {
        String message;
        if (e instanceof MethodArgumentNotValidException ex) {
            message = ex.getBindingResult().getFieldError() != null ?
                    ex.getBindingResult().getFieldError().getDefaultMessage() : "参数验证失败";
        } else {
            BindException ex = (BindException) e;
            message = ex.getBindingResult().getFieldError() != null ?
                    ex.getBindingResult().getFieldError().getDefaultMessage() : "参数验证失败";
        }
        return R.error(400, message);
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public R handleException(Exception e) {
        return R.error(500, "服务器内部错误：" + e.getMessage());
    }
}
