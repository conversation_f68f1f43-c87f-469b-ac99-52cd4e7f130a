package com.xlxw.code.common.response;

import lombok.Data;

@Data
public class R {
    private Integer code;
    private String message;
    private Object data;

    private R(Integer code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static R ok() {
        return new R(0, "操作成功", null);
    }

    public static R ok(String message) {
        return new R(0, message, null);
    }

    public static R ok(Object data) {
        return new R(0, "操作成功", data);
    }

    public static R ok(String message, Object data) {
        return new R(0, message, data);
    }

    public static R error(String message) {
        return new R(400, message, null);
    }

    public static R error(Integer code, String message) {
        return new R(code, message, null);
    }
}
