package com.xlxw.code.util;

import java.util.regex.Pattern;

/**
 * 验证工具类
 */
public class ValidationUtil {

    // 邮箱正则表达式
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
    
    // 手机号正则表达式（中国大陆）
    private static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
    
    private static final Pattern emailPattern = Pattern.compile(EMAIL_PATTERN);
    private static final Pattern phonePattern = Pattern.compile(PHONE_PATTERN);

    /**
     * 验证是否为有效邮箱
     * @param email 邮箱地址
     * @return 是否为有效邮箱
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        return emailPattern.matcher(email.trim()).matches();
    }

    /**
     * 验证是否为有效手机号
     * @param phone 手机号
     * @return 是否为有效手机号
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }
        return phonePattern.matcher(phone.trim()).matches();
    }

    /**
     * 判断联系方式类型
     * @param contact 联系方式（邮箱或手机号）
     * @return 联系方式类型
     */
    public static ContactType getContactType(String contact) {
        if (contact == null || contact.trim().isEmpty()) {
            return ContactType.INVALID;
        }
        
        String trimmedContact = contact.trim();
        
        if (isValidEmail(trimmedContact)) {
            return ContactType.EMAIL;
        } else if (isValidPhone(trimmedContact)) {
            return ContactType.PHONE;
        } else {
            return ContactType.INVALID;
        }
    }

    /**
     * 联系方式类型枚举
     */
    public enum ContactType {
        EMAIL,    // 邮箱
        PHONE,    // 手机号
        INVALID   // 无效
    }
}
