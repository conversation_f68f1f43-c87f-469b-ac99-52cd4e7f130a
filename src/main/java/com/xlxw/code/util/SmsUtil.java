package com.xlxw.code.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 短信发送工具类
 * 注意：这是一个模拟实现，实际项目中需要集成真实的短信服务商（如阿里云、腾讯云等）
 */
@Slf4j
@Component
public class SmsUtil {

    /**
     * 发送短信验证码
     * @param phone 手机号
     * @param code 验证码
     * @return 是否发送成功
     */
    public boolean sendVerificationCode(String phone, String code) {
        try {
            // TODO: 这里应该调用真实的短信服务商API
            // 例如：阿里云短信服务、腾讯云短信服务等
            
            log.info("模拟发送短信验证码到手机号: {}, 验证码: {}", phone, code);
            
            // 模拟发送过程
            Thread.sleep(100); // 模拟网络延迟
            
            // 模拟发送成功
            log.info("短信验证码发送成功");
            return true;
            
        } catch (Exception e) {
            log.error("短信验证码发送失败", e);
            return false;
        }
    }

    /**
     * 发送重置密码短信验证码
     * @param phone 手机号
     * @param code 验证码
     * @return 是否发送成功
     */
    public boolean sendResetPasswordCode(String phone, String code) {
        try {
            // TODO: 这里应该调用真实的短信服务商API
            
            log.info("模拟发送重置密码短信验证码到手机号: {}, 验证码: {}", phone, code);
            
            // 模拟发送过程
            Thread.sleep(100); // 模拟网络延迟
            
            // 模拟发送成功
            log.info("重置密码短信验证码发送成功");
            return true;
            
        } catch (Exception e) {
            log.error("重置密码短信验证码发送失败", e);
            return false;
        }
    }
}
