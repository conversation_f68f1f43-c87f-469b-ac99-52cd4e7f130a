package com.xlxw.code.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * 邮件配置检查工具
 */
@Slf4j
@Component
public class MailConfigChecker {

    @Value("${spring.mail.username:}")
    private String mailUsername;

    @Value("${spring.mail.password:}")
    private String mailPassword;

    @Value("${spring.mail.host:}")
    private String mailHost;

    @Value("${spring.mail.port:0}")
    private int mailPort;

    private boolean mailConfigured = false;

    @PostConstruct
    public void checkMailConfig() {
        log.info("检查邮件配置...");
        
        if (mailUsername == null || mailUsername.trim().isEmpty()) {
            log.warn("邮件用户名未配置 (MAIL_USERNAME)");
        }
        
        if (mailPassword == null || mailPassword.trim().isEmpty()) {
            log.warn("邮件密码未配置 (MAIL_PASSWORD)");
        }
        
        if (mailHost == null || mailHost.trim().isEmpty()) {
            log.warn("邮件服务器未配置");
        }
        
        if (mailPort == 0) {
            log.warn("邮件端口未配置");
        }
        
        mailConfigured = !mailUsername.trim().isEmpty() && 
                        !mailPassword.trim().isEmpty() && 
                        !mailHost.trim().isEmpty() && 
                        mailPort > 0;
        
        if (mailConfigured) {
            log.info("邮件配置检查通过: {}:{}", mailHost, mailPort);
        } else {
            log.warn("邮件配置不完整，将使用模拟发送模式");
            log.info("要启用真实邮件发送，请配置以下环境变量:");
            log.info("  MAIL_USERNAME=<EMAIL>");
            log.info("  MAIL_PASSWORD=your-authorization-code");
        }
    }

    /**
     * 检查邮件是否已正确配置
     * @return 是否已配置
     */
    public boolean isMailConfigured() {
        return mailConfigured;
    }

    /**
     * 获取配置状态信息
     * @return 配置状态
     */
    public String getConfigStatus() {
        if (mailConfigured) {
            return "邮件服务已配置并可用";
        } else {
            return "邮件服务未配置，使用模拟模式";
        }
    }
}
