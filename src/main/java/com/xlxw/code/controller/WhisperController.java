package com.xlxw.code.controller;

import com.xlxw.code.common.response.R;
import com.xlxw.code.pojo.dto.CommentCreateDTO;
import com.xlxw.code.pojo.dto.WhisperCreateDTO;
import com.xlxw.code.pojo.vo.CommentVO;
import com.xlxw.code.pojo.vo.PageVO;
import com.xlxw.code.pojo.vo.WhisperVO;
import com.xlxw.code.service.WhisperService;
import com.xlxw.code.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api")
@Tag(name = "树洞系统", description = "树洞相关接口")
public class WhisperController {

    @Autowired
    private WhisperService whisperService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取当前用户ID（可能为null，表示匿名访问）
     */
    private Integer getCurrentUserId(HttpServletRequest request) {
        try {
            String token = request.getHeader("Authorization");
            if (token != null && token.startsWith("Bearer ")) {
                token = token.substring(7);
                if (jwtUtil.validateToken(token)) {
                    return jwtUtil.getUserIdFromToken(token);
                }
            }
        } catch (Exception e) {
            // 忽略token解析异常，返回null表示匿名访问
            log.debug("Token解析失败，将作为匿名用户处理: {}", e.getMessage());
        }
        return null;
    }

    @Operation(summary = "发布树洞", description = "发布一条新的树洞")
    @PostMapping("/whispers")
    public R createWhisper(@Validated @RequestBody WhisperCreateDTO dto, HttpServletRequest request) {
        Integer userId = getCurrentUserId(request);
        if (userId == null) {
            return R.error(401, "未授权");
        }

        Map<String, Object> result = whisperService.createWhisper(userId, dto);
        return R.ok("树洞发布成功", result);
    }

    @Operation(summary = "拥抱树洞", description = "给树洞一个拥抱")
    @PostMapping("/whispers/{whisperId}/hug")
    public R hugWhisper(@Parameter(description = "树洞ID") @PathVariable Integer whisperId, 
                        HttpServletRequest request) {
        Integer userId = getCurrentUserId(request);
        if (userId == null) {
            return R.error(401, "未授权");
        }

        Map<String, Object> result = whisperService.hugWhisper(userId, whisperId);
        return R.ok("拥抱成功", result);
    }

    @Operation(summary = "共鸣树洞", description = "与树洞产生共鸣")
    @PostMapping("/whispers/{whisperId}/resonate")
    public R resonateWhisper(@Parameter(description = "树洞ID") @PathVariable Integer whisperId, 
                             HttpServletRequest request) {
        Integer userId = getCurrentUserId(request);
        if (userId == null) {
            return R.error(401, "未授权");
        }

        Map<String, Object> result = whisperService.resonateWhisper(userId, whisperId);
        return R.ok("共鸣成功", result);
    }

    @Operation(summary = "评论树洞", description = "对树洞进行评论")
    @PostMapping("/whispers/{whisperId}/comments")
    public R commentWhisper(@Parameter(description = "树洞ID") @PathVariable Integer whisperId,
                            @Validated @RequestBody CommentCreateDTO dto,
                            HttpServletRequest request) {
        Integer userId = getCurrentUserId(request);
        if (userId == null) {
            return R.error(401, "未授权");
        }

        Map<String, Object> result = whisperService.commentWhisper(userId, whisperId, dto);
        return R.ok("评论成功", result);
    }

    @Operation(summary = "获取树洞列表", description = "分页获取树洞列表（无需登录）")
    @GetMapping("/whispers")
    public R getWhisperList(@Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
                            @Parameter(description = "每页数量") @RequestParam(defaultValue = "5") Integer pageSize,
                            HttpServletRequest request) {
        // 获取当前用户ID（可能为null，表示匿名访问）
        Integer currentUserId = getCurrentUserId(request);
        PageVO<WhisperVO> result = whisperService.getWhisperList(page, pageSize, currentUserId);
        return R.ok(result);
    }

    @Operation(summary = "获取树洞详情", description = "获取指定树洞的详细信息（无需登录）")
    @GetMapping("/whispers/{whisperId}")
    public R getWhisperDetail(@Parameter(description = "树洞ID") @PathVariable Integer whisperId,
                              HttpServletRequest request) {
        // 获取当前用户ID（可能为null，表示匿名访问）
        Integer currentUserId = getCurrentUserId(request);
        WhisperVO result = whisperService.getWhisperDetail(whisperId, currentUserId);
        return R.ok(result);
    }

    @Operation(summary = "获取树洞评论列表", description = "分页获取指定树洞的评论列表（无需登录）")
    @GetMapping("/whispers/{whisperId}/comments")
    public R getWhisperComments(@Parameter(description = "树洞ID") @PathVariable Integer whisperId,
                                @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
                                @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer pageSize) {
        PageVO<CommentVO> result = whisperService.getWhisperComments(whisperId, page, pageSize);
        return R.ok(result);
    }
}
