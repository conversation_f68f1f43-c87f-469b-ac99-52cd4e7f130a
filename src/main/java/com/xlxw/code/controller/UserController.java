package com.xlxw.code.controller;

import com.xlxw.code.common.response.R;
import com.xlxw.code.pojo.dto.LoginDTO;
import com.xlxw.code.pojo.dto.RegisterDTO;
import com.xlxw.code.pojo.entity.User;
import com.xlxw.code.service.UserService;
import com.xlxw.code.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "用户管理", description = "用户相关接口")
@RestController
@RequestMapping("/api")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    @Operation(summary = "用户登录", description = "通过用户名和密码登录系统")
    @PostMapping("/login")
    public R login(@RequestBody @Valid LoginDTO loginDTO) {
        User user = userService.login(loginDTO);
        String token = jwtUtil.generateToken(user.getId());

        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("username", user.getUsername());
        data.put("email", user.getEmail());
        data.put("daily_whisper_limit", user.getDailyWhisperLimit());
        data.put("wechat_nickname", user.getWechatNickname());
        data.put("access_token", token);

        return R.ok("登录成功", data);
    }

    @Operation(summary = "用户注册", description = "注册新用户")
    @PostMapping("/register")
    public R register(@RequestBody @Valid RegisterDTO registerDTO) {
        User user = userService.register(registerDTO);

        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("username", user.getUsername());
        data.put("email", user.getEmail());

        return R.ok("注册成功", data);
    }

    @Operation(summary = "微信登录", description = "通过微信code登录")
    @PostMapping("/wechat-login")
    public R wechatLogin(@Parameter(description = "微信授权码") @RequestParam String code) {
        User user = userService.wechatLogin(code);
        String token = jwtUtil.generateToken(user.getId());

        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("wechat_nickname", user.getWechatNickname());
        data.put("wechat_avatar_url", user.getWechatAvatarUrl());
        data.put("daily_whisper_limit", user.getDailyWhisperLimit());
        data.put("access_token", token);

        return R.ok("微信登录成功", data);
    }

    @Operation(summary = "发送重置密码验证码", description = "向用户邮箱或手机发送验证码")
    @PostMapping("/send-reset-code")
    public R sendResetCode(@Parameter(description = "邮箱地址或手机号") @RequestParam String contact) {
        userService.sendResetCode(contact);
        return R.ok("验证码已发送");
    }

    @Operation(summary = "重置密码", description = "通过验证码重置密码")
    @PostMapping("/reset-password")
    public R resetPassword(
            @Parameter(description = "邮箱地址或手机号") @RequestParam String contact,
            @Parameter(description = "验证码") @RequestParam String verificationCode,
            @Parameter(description = "新密码") @RequestParam String newPassword) {
        userService.resetPassword(contact, verificationCode, newPassword);
        return R.ok("密码重置成功");
    }

    @Operation(summary = "获取用户信息", description = "获取当前登录用户的详细信息")
    @GetMapping("/user")
    public R getUserInfo(@Parameter(hidden = true) @RequestHeader("Authorization") String token) {
        String tokenValue = token.replace("Bearer ", "");
        Integer userId = jwtUtil.getUserIdFromToken(tokenValue);
        User user = userService.getUserInfo(userId);

        Map<String, Object> data = new HashMap<>();
        data.put("id", user.getId());
        data.put("username", user.getUsername());
        data.put("email", user.getEmail());
        data.put("phone", user.getPhone());
        data.put("wechat_nickname", user.getWechatNickname());
        data.put("daily_whisper_limit", user.getDailyWhisperLimit());
        data.put("last_login", user.getLastLogin());

        return R.ok(data);
    }

    @Operation(summary = "更新用户信息", description = "更新当前登录用户的信息")
    @PutMapping("/user")
    public R updateUserInfo(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token,
            @RequestBody User user) {
        String tokenValue = token.replace("Bearer ", "");
        Integer userId = jwtUtil.getUserIdFromToken(tokenValue);
        userService.updateUserInfo(userId, user);
        return R.ok("用户信息更新成功");
    }

    @Operation(summary = "发送注册验证码", description = "向指定邮箱发送注册验证码")
    @PostMapping("/send-register-code")
    public R sendRegisterCode(@Parameter(description = "邮箱地址") @RequestParam String email) {
        userService.sendRegisterCode(email);
        return R.ok("验证码已发送");
    }
}
